/* 自定义输入框容器样式 */
.custom-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 12px;
  padding: 4px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e1e5e9;
}

/* 自定义输入框样式 */
.custom-search-input {
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  width: 200px;
  outline: none;
  transition: border-color 0.3s ease;
}

.custom-search-input:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.custom-search-input::placeholder {
  color: #c0c4cc;
}

/* 自定义搜索按钮样式 */
.custom-search-button {
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  white-space: nowrap;
}

.custom-search-button:hover {
  background: #66b1ff;
}

.custom-search-button:active {
  background: #3a8ee6;
}

/* 确保容器在flex布局中正确显示 */
.flex-shrink-0 {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .custom-search-input {
    width: 150px;
  }
  
  .custom-input-container {
    margin-right: 8px;
  }
}

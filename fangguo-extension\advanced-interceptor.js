// 高级网络拦截器 - 使用更激进的拦截方法
console.log('🚀 高级拦截器已加载');

// 拦截配置
let interceptConfig = {
  enabled: false,
  customData: null
};

// 调试函数
function log(...args) {
  console.log('[🔥拦截器]', ...args);
}

// 初始化拦截配置
async function initConfig() {
  try {
    const [statusRes, dataRes] = await Promise.all([
      chrome.runtime.sendMessage({ action: 'getInterceptStatus' }),
      chrome.runtime.sendMessage({ action: 'getCustomJson' })
    ]);
    
    interceptConfig.enabled = statusRes?.enabled || false;
    interceptConfig.customData = dataRes?.data || null;
    
    log('配置初始化完成:', interceptConfig);
  } catch (error) {
    log('配置初始化失败:', error);
  }
}

// 检查是否是目标请求
function isTargetRequest(url, data) {
  if (!url) return false;
  
  const isTargetUrl = url.includes('check-exists') || url.includes('/fgapp/order/shop/trade/check-exists');
  
  if (isTargetUrl) {
    log('🎯 发现目标请求:', url);
    if (data) {
      try {
        const parsed = typeof data === 'string' ? JSON.parse(data) : data;
        log('请求数据包含:', Object.keys(parsed));
      } catch (e) {
        log('请求数据:', data);
      }
    }
  }
  
  return isTargetUrl;
}

// 方法1: 重写原生方法
function setupNativeInterception() {
  // 保存原始方法
  const originalFetch = window.fetch;
  const originalXHRSend = XMLHttpRequest.prototype.send;
  const originalXHROpen = XMLHttpRequest.prototype.open;
  
  // 重写fetch
  window.fetch = function(input, init = {}) {
    const url = typeof input === 'string' ? input : input.url;
    
    if (isTargetRequest(url, init.body)) {
      if (interceptConfig.enabled && interceptConfig.customData) {
        log('🚫 FETCH拦截生效!');
        init.body = JSON.stringify(interceptConfig.customData);
        log('替换数据:', init.body);
      }
    }
    
    return originalFetch.call(this, input, init);
  };
  
  // 重写XMLHttpRequest
  XMLHttpRequest.prototype.open = function(method, url, ...args) {
    this._interceptUrl = url;
    this._interceptMethod = method;
    return originalXHROpen.apply(this, [method, url, ...args]);
  };
  
  XMLHttpRequest.prototype.send = function(data) {
    if (isTargetRequest(this._interceptUrl, data)) {
      if (interceptConfig.enabled && interceptConfig.customData) {
        log('🚫 XHR拦截生效!');
        data = JSON.stringify(interceptConfig.customData);
        log('替换数据:', data);
      }
    }
    
    return originalXHRSend.call(this, data);
  };
  
  log('✅ 原生方法拦截已设置');
}

// 方法2: 使用Proxy拦截
function setupProxyInterception() {
  // 拦截fetch
  if (window.fetch) {
    const originalFetch = window.fetch;
    window.fetch = new Proxy(originalFetch, {
      apply: function(target, thisArg, argumentsList) {
        const [input, init = {}] = argumentsList;
        const url = typeof input === 'string' ? input : input.url;
        
        if (isTargetRequest(url, init.body)) {
          if (interceptConfig.enabled && interceptConfig.customData) {
            log('🚫 PROXY FETCH拦截生效!');
            init.body = JSON.stringify(interceptConfig.customData);
          }
        }
        
        return target.apply(thisArg, [input, init]);
      }
    });
  }
  
  log('✅ Proxy拦截已设置');
}

// 方法3: 监听网络事件
function setupEventInterception() {
  // 监听所有网络请求
  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.name && isTargetRequest(entry.name)) {
        log('🌐 检测到网络请求:', entry.name);
      }
    }
  });
  
  try {
    observer.observe({ entryTypes: ['resource'] });
    log('✅ 性能监听已设置');
  } catch (e) {
    log('性能监听设置失败:', e);
  }
}

// 消息监听
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  log('收到消息:', request.action);
  
  switch (request.action) {
    case 'updateInterceptStatus':
      interceptConfig.enabled = request.enabled;
      log('🔄 拦截状态更新:', interceptConfig.enabled);
      sendResponse({ success: true });
      break;
      
    case 'updateCustomJson':
      interceptConfig.customData = request.data;
      log('🔄 自定义数据更新');
      sendResponse({ success: true });
      break;
      
    case 'getInterceptorStatus':
      sendResponse({
        enabled: interceptConfig.enabled,
        hasCustomData: !!interceptConfig.customData
      });
      break;
      
    case 'forceRefresh':
      initConfig();
      sendResponse({ success: true });
      break;
  }
  
  return true;
});

// 初始化所有拦截方法
async function initialize() {
  log('🚀 开始初始化高级拦截器...');
  
  await initConfig();
  
  // 设置多种拦截方法
  setupNativeInterception();
  setupProxyInterception();
  setupEventInterception();
  
  log('✅ 高级拦截器初始化完成');
  
  // 定期检查配置
  setInterval(async () => {
    if (!interceptConfig.enabled && !interceptConfig.customData) {
      await initConfig();
    }
  }, 5000);
}

// 立即执行
initialize();

// 页面加载完成后再次初始化
if (document.readyState !== 'complete') {
  window.addEventListener('load', initialize);
}

log('🔥 高级拦截器脚本加载完成');

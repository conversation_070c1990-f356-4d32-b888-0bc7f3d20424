import {
    d as e,
    r as a,
    Z as d,
    w as i,
    g as m,
    q as l,
    v as t,
    c as n,
    U as r,
    T as s,
    n as o,
    e as u,
    h5 as p,
    _ as c
} from "./index-PITnHt5Y.js"; /* empty css              */ /* empty css            */
import {
    E as y,
    b as f,
    a as h
} from "./select-D0Ysbh2X.js"; /* empty css                  */ /* empty css               */
import {
    o as v
} from "./order-BLlr5hsb.js";
import {
    y as b,
    z as j,
    A as g,
    D as k,
    E as S,
    H as V
} from "./enums-BQfqlMjU.js";
import "./index-ClQNu5pa.js";
import "./index-BKVJYL4F.js";
import "./util-CyoWC6l2.js";
import "./index-BgeYWFc3.js";
import "./strings-OM41d7cj.js";
import "./debounce-BlDc_s1D.js";
import "./_baseFindIndex-BTqrMG7V.js";
import "./_baseIteratee-CPEO3oEA.js";
import "./index-zBmKjUyn.js";
import "./order-1J6GWiyf.js";
import "./index-C45PtPps.js";
import "./storePick-B3x7-wa7.js";
const I = c(e({
    __name: "CustomSelect",
    props: {
        name: {
            type: String,
            default: "请输入"
        },
        modelValue1: {
            type: String,
            required: !0
        },
        multiple: {
            type: Boolean,
            default: !1
        },
        clear: {
            type: Boolean,
            default: !0
        }
    },
    setup(e) {
        const c = e,
            I = a(c.multiple),
            L = v(),
            {
                ListParams: O,
                BasicData: T
            } = d(L),
            P = [{
                id: 1,
                name: "是"
            }, {
                id: 0,
                name: "否"
            }],
            _ = a({
                orderType: b,
                remarkQuery: j,
                cpCode: [],
                waybillType: [{
                    id: 1,
                    name: "超级面单"
                }, {
                    id: 0,
                    name: "工厂面单"
                }],
                outerOrderStatusList: g,
                shipStatusOrder: [{
                    id: 0,
                    name: "未打包"
                }, {
                    id: 1,
                    name: "已打包"
                }, {
                    id: 2,
                    name: "未推单"
                }, {
                    id: 3,
                    name: "已推单"
                }],
                existCpNum: [{
                    id: !0,
                    name: "有快递单号"
                }, {
                    id: !1,
                    name: "无快递单号"
                }],
                waybillPrintStatus: [{
                    id: !0,
                    name: "已打印"
                }, {
                    id: !1,
                    name: "未打印"
                }],
                lockStatusOrder: [{
                    id: !0,
                    name: "已锁单"
                }, {
                    id: !1,
                    name: "未锁单"
                }],
                discardStatusOrder: [{
                    id: 0,
                    name: "未作废"
                }, {
                    id: 1,
                    name: "已作废"
                }, {
                    id: 2,
                    name: "生产中已退款"
                }, {
                    id: 3,
                    name: "已打包已退款"
                }],
                milePackageStatus: k,
                isPicNum: [{
                    id: 1,
                    name: "图号正确"
                }, {
                    id: 0,
                    name: "图号错误"
                }],
                urgencyType: [{
                    id: 1,
                    name: "紧急备货单"
                }, {
                    id: 0,
                    name: "普通备货单"
                }],
                sheinSettlementType: [{
                    id: 1,
                    name: "备货"
                }, {
                    id: 0,
                    name: "急采"
                }],
                jitFlag: P,
                vmiFlag: P,
                factoryId: [],
                isCustomGoods: P,
                custodyType: [{
                    id: 2,
                    name: "全托管"
                }, {
                    id: 1,
                    name: "半托管"
                }],
                logisticsStatus: [{
                    id: 0,
                    name: "未揽收"
                }, {
                    id: 1,
                    name: "已揽收"
                }, {
                    id: 2,
                    name: "运输中"
                }, {
                    id: 3,
                    name: "派送中"
                }, {
                    id: 4,
                    name: "已签收"
                }],
                decodeStatus: [{
                    id: 1,
                    name: "解码成功"
                }, {
                    id: 2,
                    name: "解码失败"
                }],
                tagListQuery: S,
                factoryLackPicStatus: [{
                    id: !0,
                    name: "是"
                }, {
                    id: !1,
                    name: "否"
                }],
                decodeErrorTypeList: [{
                    id: 1,
                    name: "未填写四要素的失败原因",
                    children: [{
                        id: 1,
                        name: "未填写【材质】编码"
                    }, {
                        id: 2,
                        name: "未填写【颜色】编码"
                    }, {
                        id: 4,
                        name: "未填写【型号】编码"
                    }, {
                        id: 8,
                        name: "未填写【图号】编码"
                    }]
                }, {
                    id: 2,
                    name: "四要素在商家编码库中不存在的失败原因",
                    children: [{
                        id: 16,
                        name: "【材质】商家编码库中不存在"
                    }, {
                        id: 32,
                        name: "【颜色】商家编码库中不存在"
                    }, {
                        id: 64,
                        name: "【型号】商家编码库中不存在"
                    }, {
                        id: 128,
                        name: "【图号】商家编码库中不存在"
                    }]
                }, {
                    id: 3,
                    name: "四要素组成的SKU在工厂端未上架",
                    children: [{
                        id: 256,
                        name: "工厂未上架该商品"
                    }, {
                        id: 512,
                        name: "工厂已下架该商品"
                    }]
                }, {
                    id: 4,
                    name: "图号编码有问题的失败原因",
                    children: [{
                        id: 1024,
                        name: "图号必须传线上图推送"
                    }, {
                        id: 2048,
                        name: "图号未含有姓氏信息"
                    }]
                }, {
                    id: 5,
                    name: "赠品编码有问题的失败原因",
                    children: [{
                        id: 4096,
                        name: "【赠品】商家编码库中不存在"
                    }, {
                        id: 8192,
                        name: "【赠品】在工厂编码映射失败"
                    }]
                }, {
                    id: 6,
                    name: "赠品打图有问题的失败原因",
                    children: [{
                        id: 16384,
                        name: "【赠品】没有选择赠品图片"
                    }, {
                        id: 32768,
                        name: "【赠品】图片在图库中不存在"
                    }, {
                        id: 65536,
                        name: "【赠品】图片必须传线上图推送"
                    }]
                }, {
                    id: 7,
                    name: "四要素在商家编码中存在，但和工厂映射失败",
                    children: [{
                        id: 131072,
                        name: "【材质】编码在工厂映射失败"
                    }, {
                        id: 262144,
                        name: "【颜色】编码在工厂映射失败"
                    }, {
                        id: 524288,
                        name: "【型号】编码在工厂映射失败"
                    }]
                }],
                autoPush: [{
                    id: 0,
                    name: "手动推送"
                }, {
                    id: 1,
                    name: "自动推送"
                }],
                inventoryStatus: [{
                    id: 1,
                    name: "缺货"
                }, {
                    id: 2,
                    name: "有货"
                }],
                jitSendStatus: V,
                hasGift: [{
                    id: !0,
                    name: "有赠品"
                }, {
                    id: !1,
                    name: "无赠品"
                }],
                combinationGoodsType: [{
                    id: 0,
                    name: "含有组合商品"
                }, {
                    id: 1,
                    name: "没有组合商品"
                }],
                deductWarehouseName: T.value.shopTradefilter,
                customOrderRemark: T.value.customOrderRemarks
            });
        return ["cpCode", "factoryId"].includes(c.modelValue1) && i((() => T.value), (e => {
            _.value.cpCode = (e.logisticsList || []).map((e => ({
                ...e,
                id: e.cpCode,
                name: e.cpName
            }))), _.value.factoryId = e.factoryVOList || []
        }), {
            immediate: !0,
            deep: !0
        }), "tagListQuery" === c.modelValue1 && i((() => T.value), (e => {
            const a = S.filter((a => 119 === a.platformId || 219 === a.platformId ? e.bindPlatformIdList.includes(119) || e.bindPlatformIdList.includes(219) : e.bindPlatformIdList.includes(a.platformId))).reduce(((e, {
                    id: a,
                    name: d,
                    platformId: i,
                    platformName: m
                }) => (e[i] || (e[i] = {
                    platformId: i,
                    platformName: m,
                    children: []
                }), e[i].children.push({
                    id: a,
                    name: d
                }), e)), {}),
                d = Object.values(a);
            _.value.tagListQuery = d.sort(((e, a) => Math.min(...e.children.map((e => e.id))) - Math.min(...a.children.map((e => e.id)))))
        }), {
            immediate: !0,
            deep: !0
        }), "deductWarehouseName" === c.modelValue1 && i((() => T.value), (e => {
            _.value.deductWarehouseName = e.shopTradefilter
        }), {
            immediate: !0,
            deep: !0
        }), "customOrderRemark" === c.modelValue1 && i((() => T.value), (e => {
            _.value.customOrderRemark = e.customOrderRemarks
        }), {
            immediate: !0,
            deep: !0
        }), (a, d) => {
            const i = y,
                c = f,
                v = h;
            return m(), l(v, {
                class: o(["custom-select", {
                    active: u(p)(u(O)[e.modelValue1])
                }]),
                modelValue: u(O)[e.modelValue1],
                "onUpdate:modelValue": d[0] || (d[0] = a => u(O)[e.modelValue1] = a),
                placeholder: e.name,
                multiple: I.value,
                "collapse-tags": "",
                "collapse-tags-tooltip": "",
                clearable: e.clear
            }, {
                default: t((() => ["tagListQuery" === e.modelValue1 ? (m(!0), n(s, {
                    key: 0
                }, r(_.value[e.modelValue1] || [], (e => (m(), l(c, {
                    key: e.platformId,
                    label: e.platformName
                }, {
                    default: t((() => [(m(!0), n(s, null, r(e.children || [], (e => (m(), l(i, {
                        key: e.id,
                        label: e.name,
                        value: e.id
                    }, null, 8, ["label", "value"])))), 128))])),
                    _: 2
                }, 1032, ["label"])))), 128)) : "decodeErrorTypeList" === e.modelValue1 ? (m(!0), n(s, {
                    key: 1
                }, r(_.value[e.modelValue1] || [], (e => (m(), l(c, {
                    key: e.id,
                    label: e.name
                }, {
                    default: t((() => [(m(!0), n(s, null, r(e.children || [], (e => (m(), l(i, {
                        key: e.id,
                        label: e.name,
                        value: e.id
                    }, null, 8, ["label", "value"])))), 128))])),
                    _: 2
                }, 1032, ["label"])))), 128)) : (m(!0), n(s, {
                    key: 2
                }, r(_.value[e.modelValue1] || [], (e => (m(), l(i, {
                    key: e.id,
                    label: e.name,
                    value: e.id
                }, null, 8, ["label", "value"])))), 128))])),
                _: 1
            }, 8, ["class", "modelValue", "placeholder", "multiple", "clearable"])
        }
    }
}), [
    ["__scopeId", "data-v-5f016026"]
]);
export {
    I as
    default
};
console.log('Fangguo扩展内容脚本已加载');

// 等待页面加载完成
function waitForElement(selector, timeout = 10000) {
  return new Promise((resolve, reject) => {
    const element = document.querySelector(selector);
    if (element) {
      resolve(element);
      return;
    }

    const observer = new MutationObserver((mutations, obs) => {
      const element = document.querySelector(selector);
      if (element) {
        obs.disconnect();
        resolve(element);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    setTimeout(() => {
      observer.disconnect();
      reject(new Error(`Element ${selector} not found within ${timeout}ms`));
    }, timeout);
  });
}

// 创建自定义输入框
function createCustomInput() {
  const inputContainer = document.createElement('div');
  inputContainer.className = 'custom-input-container';
  inputContainer.setAttribute('data-fangguo-extension', 'true');
  
  const input = document.createElement('input');
  input.type = 'text';
  input.placeholder = '请输入搜索内容...';
  input.className = 'custom-search-input';
  input.id = 'fangguo-custom-input';
  
  const searchButton = document.createElement('button');
  searchButton.textContent = '自定义搜索';
  searchButton.className = 'custom-search-button';
  searchButton.onclick = function() {
    const value = input.value.trim();
    if (value) {
      console.log('自定义搜索:', value);
      // 这里可以添加自定义搜索逻辑
      alert(`搜索内容: ${value}`);
    }
  };
  
  inputContainer.appendChild(input);
  inputContainer.appendChild(searchButton);
  
  return inputContainer;
}

// 添加输入框到指定位置
async function addCustomInput() {
  try {
    console.log('开始查找目标元素...');
    
    // 查找包含按钮的容器
    const targetSelector = 'div.flex-shrink-0';
    const targetElement = await waitForElement(targetSelector);
    
    console.log('找到目标元素:', targetElement);
    
    // 检查是否已经添加过输入框
    if (document.querySelector('[data-fangguo-extension="true"]')) {
      console.log('输入框已存在，跳过添加');
      return;
    }
    
    // 创建并插入自定义输入框
    const customInput = createCustomInput();
    
    // 在目标元素之前插入
    targetElement.parentNode.insertBefore(customInput, targetElement);
    
    console.log('自定义输入框已添加');
    
  } catch (error) {
    console.error('添加自定义输入框失败:', error);
    
    // 如果找不到精确的元素，尝试更通用的选择器
    setTimeout(() => {
      const buttons = document.querySelectorAll('button.el-button');
      if (buttons.length > 0) {
        const lastButton = buttons[buttons.length - 1];
        const container = lastButton.closest('div');
        if (container && !document.querySelector('[data-fangguo-extension="true"]')) {
          const customInput = createCustomInput();
          container.appendChild(customInput);
          console.log('使用备用方法添加了自定义输入框');
        }
      }
    }, 2000);
  }
}

// 检查URL并执行
function checkUrlAndExecute() {
  const currentUrl = window.location.href;
  console.log('当前URL:', currentUrl);
  
  if (currentUrl.includes('fangguo.com/business/order')) {
    console.log('检测到目标页面，准备添加输入框');
    addCustomInput();
  }
}

// 监听URL变化（适用于SPA应用）
let lastUrl = location.href;
new MutationObserver(() => {
  const url = location.href;
  if (url !== lastUrl) {
    lastUrl = url;
    console.log('URL变化检测到:', url);
    setTimeout(checkUrlAndExecute, 1000); // 延迟执行以确保页面加载完成
  }
}).observe(document, { subtree: true, childList: true });

// 页面加载完成后执行
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', checkUrlAndExecute);
} else {
  checkUrlAndExecute();
}

// 额外的延迟检查，确保动态内容加载完成
setTimeout(checkUrlAndExecute, 3000);

// 监听来自弹出窗口的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'ping') {
    // 响应ping消息，确认内容脚本已加载
    sendResponse({ loaded: true });
  } else if (request.action === 'refresh') {
    console.log('收到刷新请求');
    checkUrlAndExecute();
    sendResponse({ status: 'refreshed' });
  } else if (request.action === 'toggle') {
    console.log('收到切换请求');
    const existingInput = document.querySelector('[data-fangguo-extension="true"]');
    if (existingInput) {
      existingInput.remove();
      console.log('已移除自定义输入框');
      sendResponse({ status: 'removed' });
    } else {
      checkUrlAndExecute();
      sendResponse({ status: 'added' });
    }
  }
  return true; // 保持消息通道开放
});

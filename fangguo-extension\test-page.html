<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>拦截器测试页面</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    
    .test-section {
      margin: 20px 0;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    
    button {
      padding: 10px 15px;
      margin: 5px;
      border: none;
      border-radius: 3px;
      cursor: pointer;
      background: #007cba;
      color: white;
    }
    
    button:hover {
      background: #005a87;
    }
    
    .log {
      background: #f5f5f5;
      padding: 10px;
      border-radius: 3px;
      margin: 10px 0;
      font-family: monospace;
      white-space: pre-wrap;
      max-height: 300px;
      overflow-y: auto;
    }
    
    .status {
      padding: 10px;
      margin: 10px 0;
      border-radius: 3px;
    }
    
    .status.success {
      background: #d4edda;
      color: #155724;
    }
    
    .status.error {
      background: #f8d7da;
      color: #721c24;
    }
  </style>
</head>
<body>
  <h1>Fangguo扩展拦截器测试</h1>
  
  <div class="test-section">
    <h3>拦截器状态</h3>
    <div id="status" class="status">检查中...</div>
    <button onclick="checkStatus()">检查状态</button>
    <button onclick="toggleIntercept()">切换拦截</button>
  </div>
  
  <div class="test-section">
    <h3>测试请求</h3>
    <button onclick="testFetch()">测试 Fetch 请求</button>
    <button onclick="testXHR()">测试 XHR 请求</button>
    <button onclick="testRealAPI()">测试真实API</button>
    <button onclick="clearLog()">清空日志</button>
  </div>
  
  <div class="test-section">
    <h3>请求日志</h3>
    <div id="log" class="log">等待测试...</div>
  </div>
  
  <script>
    let interceptEnabled = false;
    
    function log(message) {
      const logElement = document.getElementById('log');
      const timestamp = new Date().toLocaleTimeString();
      logElement.textContent += `[${timestamp}] ${message}\n`;
      logElement.scrollTop = logElement.scrollHeight;
    }
    
    function updateStatus(message, isSuccess = true) {
      const statusElement = document.getElementById('status');
      statusElement.textContent = message;
      statusElement.className = `status ${isSuccess ? 'success' : 'error'}`;
    }
    
    async function checkStatus() {
      try {
        const response = await chrome.runtime.sendMessage({ action: 'getInterceptStatus' });
        interceptEnabled = response.enabled;
        updateStatus(`拦截状态: ${interceptEnabled ? '开启' : '关闭'}`, true);
        log(`拦截状态检查: ${interceptEnabled ? '开启' : '关闭'}`);
      } catch (error) {
        updateStatus('无法检查状态: ' + error.message, false);
        log('状态检查失败: ' + error.message);
      }
    }
    
    async function toggleIntercept() {
      try {
        await chrome.runtime.sendMessage({ 
          action: 'setInterceptStatus', 
          enabled: !interceptEnabled 
        });
        await checkStatus();
        log(`拦截状态已切换为: ${!interceptEnabled ? '开启' : '关闭'}`);
      } catch (error) {
        log('切换拦截状态失败: ' + error.message);
      }
    }
    
    async function testFetch() {
      log('开始测试 Fetch 请求...');
      
      const testData = {
        pageNo: 1,
        pageSize: 50,
        test: 'fetch-request'
      };
      
      try {
        const response = await fetch('https://fangguo.com/fgapp/order/shop/trade/check-exists', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(testData)
        });
        
        log(`Fetch 请求完成，状态: ${response.status}`);
      } catch (error) {
        log(`Fetch 请求失败: ${error.message}`);
      }
    }
    
    async function testXHR() {
      log('开始测试 XHR 请求...');
      
      const testData = {
        pageNo: 1,
        pageSize: 50,
        test: 'xhr-request'
      };
      
      const xhr = new XMLHttpRequest();
      xhr.open('POST', 'https://fangguo.com/fgapp/order/shop/trade/check-exists');
      xhr.setRequestHeader('Content-Type', 'application/json');
      
      xhr.onload = function() {
        log(`XHR 请求完成，状态: ${xhr.status}`);
      };
      
      xhr.onerror = function() {
        log('XHR 请求失败');
      };
      
      xhr.send(JSON.stringify(testData));
    }
    
    async function testRealAPI() {
      log('测试真实API请求...');
      // 这里可以添加真实的API测试
      log('请在实际的fangguo.com页面中测试');
    }
    
    function clearLog() {
      document.getElementById('log').textContent = '';
    }
    
    // 页面加载时检查状态
    window.addEventListener('load', checkStatus);
    
    // 监听拦截器消息
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        if (request.action === 'interceptorTest') {
          log('收到拦截器测试消息: ' + JSON.stringify(request));
          sendResponse({ received: true });
        }
        return true;
      });
    }
  </script>
</body>
</html>

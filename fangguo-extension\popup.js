// 检查当前标签页状态
async function checkCurrentTab() {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    const statusElement = document.getElementById('status');
    const statusText = document.getElementById('status-text');
    
    if (tab.url && tab.url.includes('fangguo.com/business/order')) {
      statusElement.className = 'status active';
      statusText.textContent = '✓ 已检测到目标页面';
    } else {
      statusElement.className = 'status inactive';
      statusText.textContent = '未检测到目标页面';
    }
  } catch (error) {
    console.error('检查标签页失败:', error);
  }
}

// 刷新检测
async function refreshDetection() {
  await checkCurrentTab();
  
  // 向内容脚本发送消息，重新执行检测
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (tab.url && tab.url.includes('fangguo.com')) {
      await chrome.tabs.sendMessage(tab.id, { action: 'refresh' });
    }
  } catch (error) {
    console.error('发送消息失败:', error);
  }
}

// 切换功能
async function toggleFeature() {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (tab.url && tab.url.includes('fangguo.com')) {
      await chrome.tabs.sendMessage(tab.id, { action: 'toggle' });
    }
  } catch (error) {
    console.error('切换功能失败:', error);
  }
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', async () => {
  await checkCurrentTab();
  
  // 绑定事件
  document.getElementById('refresh-btn').addEventListener('click', refreshDetection);
  document.getElementById('toggle-btn').addEventListener('click', toggleFeature);
});

// 监听标签页更新
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete') {
    checkCurrentTab();
  }
});

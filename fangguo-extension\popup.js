// 检查当前标签页状态
async function checkCurrentTab() {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    const statusElement = document.getElementById('status');
    const statusText = document.getElementById('status-text');

    if (tab.url && tab.url.includes('fangguo.com/business/order')) {
      statusElement.className = 'status active';
      statusText.textContent = '✓ 已检测到目标页面';
    } else {
      statusElement.className = 'status inactive';
      statusText.textContent = '未检测到目标页面';
    }
  } catch (error) {
    console.error('检查标签页失败:', error);
  }
}

// 检查拦截状态
async function checkInterceptStatus() {
  try {
    const response = await chrome.runtime.sendMessage({ action: 'getInterceptStatus' });
    const interceptElement = document.getElementById('intercept-status');
    const interceptText = document.getElementById('intercept-text');
    const interceptBtn = document.getElementById('intercept-btn');

    if (response.enabled) {
      interceptElement.className = 'status active';
      interceptText.textContent = '✓ 网络拦截已开启';
      interceptBtn.textContent = '关闭拦截';
    } else {
      interceptElement.className = 'status inactive';
      interceptText.textContent = '网络拦截已关闭';
      interceptBtn.textContent = '开启拦截';
    }
  } catch (error) {
    console.error('检查拦截状态失败:', error);
  }
}

// 检查内容脚本是否已加载
async function isContentScriptLoaded(tabId) {
  try {
    // 使用一个简单的ping消息来检查内容脚本是否响应
    const response = await chrome.tabs.sendMessage(tabId, { action: 'ping' })
      .catch(() => ({ loaded: false }));
    return response && response.loaded === true;
  } catch (error) {
    return false;
  }
}

// 注入内容脚本
async function injectContentScript(tabId) {
  try {
    await chrome.scripting.executeScript({
      target: { tabId },
      files: ['content.js']
    });
    console.log('内容脚本已注入');
    return true;
  } catch (error) {
    console.error('注入内容脚本失败:', error);
    return false;
  }
}

// 刷新检测
async function refreshDetection() {
  await checkCurrentTab();

  // 向内容脚本发送消息，重新执行检测
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (tab.url && tab.url.includes('fangguo.com')) {
      // 检查内容脚本是否已加载，如果没有则注入
      const isLoaded = await isContentScriptLoaded(tab.id);
      if (!isLoaded) {
        const injected = await injectContentScript(tab.id);
        if (!injected) {
          console.log('无法注入内容脚本，请确保页面已完全加载');
          return;
        }
        // 给脚本一点时间初始化
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      await chrome.tabs.sendMessage(tab.id, { action: 'refresh' })
        .catch(error => {
          console.error('发送消息失败:', error);
        });
    }
  } catch (error) {
    console.error('刷新检测失败:', error);
  }
}

// 切换功能
async function toggleFeature() {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (tab.url && tab.url.includes('fangguo.com')) {
      // 检查内容脚本是否已加载，如果没有则注入
      const isLoaded = await isContentScriptLoaded(tab.id);
      if (!isLoaded) {
        const injected = await injectContentScript(tab.id);
        if (!injected) {
          console.log('无法注入内容脚本，请确保页面已完全加载');
          return;
        }
        // 给脚本一点时间初始化
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      await chrome.tabs.sendMessage(tab.id, { action: 'toggle' })
        .catch(error => {
          console.error('切换功能失败:', error);
        });
    }
  } catch (error) {
    console.error('切换功能失败:', error);
  }
}

// 切换拦截状态
async function toggleIntercept() {
  try {
    const statusResponse = await chrome.runtime.sendMessage({ action: 'getInterceptStatus' });
    const newStatus = !statusResponse.enabled;

    await chrome.runtime.sendMessage({
      action: 'setInterceptStatus',
      enabled: newStatus
    });

    // 通知当前标签页的所有拦截器
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (tab.url && tab.url.includes('fangguo.com')) {
      // 通知所有拦截器
      const messages = [
        { action: 'updateInterceptStatus', enabled: newStatus },
        { action: 'forceRefresh' }
      ];

      for (const message of messages) {
        await chrome.tabs.sendMessage(tab.id, message).catch(() => {
          console.log('无法通知拦截器:', message.action);
        });
      }
    }

    await checkInterceptStatus();

    // 显示状态提示
    const statusText = newStatus ? '拦截已开启' : '拦截已关闭';
    console.log(statusText);

  } catch (error) {
    console.error('切换拦截状态失败:', error);
  }
}

// 编辑JSON数据
async function editJsonData() {
  try {
    // 打开JSON编辑器页面
    chrome.tabs.create({
      url: chrome.runtime.getURL('json-editor.html')
    });
  } catch (error) {
    console.error('打开JSON编辑器失败:', error);
  }
}

// 打开测试页面
async function openTestPage() {
  try {
    chrome.tabs.create({
      url: chrome.runtime.getURL('test-page.html')
    });
  } catch (error) {
    console.error('打开测试页面失败:', error);
  }
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', async () => {
  await checkCurrentTab();
  await checkInterceptStatus();

  // 绑定事件
  document.getElementById('refresh-btn').addEventListener('click', refreshDetection);
  document.getElementById('toggle-btn').addEventListener('click', toggleFeature);
  document.getElementById('intercept-btn').addEventListener('click', toggleIntercept);
  document.getElementById('edit-json-btn').addEventListener('click', editJsonData);
  document.getElementById('test-btn').addEventListener('click', openTestPage);
});

// 监听标签页更新
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete') {
    checkCurrentTab();
    checkInterceptStatus();
  }
});

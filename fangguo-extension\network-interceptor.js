// 网络请求拦截器
console.log('网络拦截器已加载');

// 目标API URL
const TARGET_API_URL = 'https://fangguo.com/fgapp/order/shop/trade/check-exists';

// 拦截状态
let interceptEnabled = false;
let customJsonData = null;

// 获取拦截状态和自定义数据
async function initializeInterceptor() {
  try {
    // 获取拦截状态
    const statusResponse = await chrome.runtime.sendMessage({ action: 'getInterceptStatus' });
    interceptEnabled = statusResponse.enabled;
    
    // 获取自定义JSON数据
    const jsonResponse = await chrome.runtime.sendMessage({ action: 'getCustomJson' });
    customJsonData = jsonResponse.data;
    
    console.log('拦截器初始化完成:', { interceptEnabled, customJsonData });
  } catch (error) {
    console.error('拦截器初始化失败:', error);
  }
}

// 原始的fetch函数
const originalFetch = window.fetch;

// 重写fetch函数
window.fetch = async function(...args) {
  const [resource, config] = args;
  
  // 检查是否是目标API
  if (typeof resource === 'string' && resource.includes('/fgapp/order/shop/trade/check-exists')) {
    console.log('检测到目标API请求:', resource);
    
    if (interceptEnabled && customJsonData) {
      console.log('拦截请求并替换载荷');
      
      // 创建新的配置，替换请求体
      const newConfig = {
        ...config,
        body: JSON.stringify(customJsonData)
      };
      
      console.log('原始请求体:', config?.body);
      console.log('替换后请求体:', newConfig.body);
      
      // 使用新的配置发送请求
      return originalFetch(resource, newConfig);
    }
  }
  
  // 对于其他请求，使用原始fetch
  return originalFetch.apply(this, args);
};

// 重写XMLHttpRequest
const originalXHROpen = XMLHttpRequest.prototype.open;
const originalXHRSend = XMLHttpRequest.prototype.send;

XMLHttpRequest.prototype.open = function(method, url, ...args) {
  this._url = url;
  this._method = method;
  return originalXHROpen.apply(this, [method, url, ...args]);
};

XMLHttpRequest.prototype.send = function(data) {
  if (this._url && this._url.includes('/fgapp/order/shop/trade/check-exists')) {
    console.log('检测到XHR目标API请求:', this._url);
    
    if (interceptEnabled && customJsonData) {
      console.log('拦截XHR请求并替换载荷');
      console.log('原始数据:', data);
      console.log('替换后数据:', JSON.stringify(customJsonData));
      
      return originalXHRSend.call(this, JSON.stringify(customJsonData));
    }
  }
  
  return originalXHRSend.call(this, data);
};

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'updateInterceptStatus') {
    interceptEnabled = request.enabled;
    console.log('拦截状态已更新:', interceptEnabled);
    sendResponse({ success: true });
  }
  
  if (request.action === 'updateCustomJson') {
    customJsonData = request.data;
    console.log('自定义JSON数据已更新:', customJsonData);
    sendResponse({ success: true });
  }
  
  if (request.action === 'getInterceptorStatus') {
    sendResponse({ 
      enabled: interceptEnabled, 
      hasCustomData: !!customJsonData 
    });
  }
  
  return true;
});

// 初始化
initializeInterceptor();

// 页面加载完成后再次初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeInterceptor);
} else {
  initializeInterceptor();
}

// 网络请求拦截器
console.log('网络拦截器已加载 - 时间:', new Date().toISOString());

// 目标API URL
const TARGET_API_URL = 'https://fangguo.com/fgapp/order/shop/trade/check-exists';

// 拦截状态
let interceptEnabled = false;
let customJsonData = null;

// 添加调试标志
const DEBUG = true;

function debugLog(...args) {
  if (DEBUG) {
    console.log('[Fangguo拦截器]', ...args);
  }
}

// 获取拦截状态和自定义数据
async function initializeInterceptor() {
  try {
    debugLog('开始初始化拦截器...');

    // 获取拦截状态
    const statusResponse = await chrome.runtime.sendMessage({ action: 'getInterceptStatus' });
    interceptEnabled = statusResponse.enabled;
    debugLog('拦截状态:', interceptEnabled);

    // 获取自定义JSON数据
    const jsonResponse = await chrome.runtime.sendMessage({ action: 'getCustomJson' });
    customJsonData = jsonResponse.data;
    debugLog('自定义JSON数据:', customJsonData);

    debugLog('拦截器初始化完成');
  } catch (error) {
    debugLog('拦截器初始化失败:', error);
  }
}

// 保存原始函数
const originalFetch = window.fetch;
const originalXHROpen = XMLHttpRequest.prototype.open;
const originalXHRSend = XMLHttpRequest.prototype.send;

// 检查是否是目标URL
function isTargetUrl(url) {
  return url && (
    url.includes('/fgapp/order/shop/trade/check-exists') ||
    url.includes('check-exists')
  );
}

// 重写fetch函数
window.fetch = async function(...args) {
  const [resource, config] = args;

  debugLog('Fetch请求:', resource);

  // 检查是否是目标API
  if (isTargetUrl(resource)) {
    debugLog('🎯 检测到目标API请求:', resource);
    debugLog('拦截状态:', interceptEnabled);
    debugLog('有自定义数据:', !!customJsonData);

    if (interceptEnabled && customJsonData) {
      debugLog('🚫 拦截请求并替换载荷');

      // 创建新的配置，替换请求体
      const newConfig = {
        ...config,
        body: JSON.stringify(customJsonData)
      };

      debugLog('原始请求体:', config?.body);
      debugLog('替换后请求体:', newConfig.body);

      // 使用新的配置发送请求
      return originalFetch(resource, newConfig);
    } else {
      debugLog('⚠️ 拦截未启用或无自定义数据');
    }
  }

  // 对于其他请求，使用原始fetch
  return originalFetch.apply(this, args);
};

// 重写XMLHttpRequest
XMLHttpRequest.prototype.open = function(method, url, ...args) {
  this._url = url;
  this._method = method;
  debugLog('XHR Open:', method, url);
  return originalXHROpen.apply(this, [method, url, ...args]);
};

XMLHttpRequest.prototype.send = function(data) {
  debugLog('XHR Send:', this._url, data);

  if (isTargetUrl(this._url)) {
    debugLog('🎯 检测到XHR目标API请求:', this._url);
    debugLog('拦截状态:', interceptEnabled);
    debugLog('有自定义数据:', !!customJsonData);

    if (interceptEnabled && customJsonData) {
      debugLog('🚫 拦截XHR请求并替换载荷');
      debugLog('原始数据:', data);
      debugLog('替换后数据:', JSON.stringify(customJsonData));

      return originalXHRSend.call(this, JSON.stringify(customJsonData));
    } else {
      debugLog('⚠️ XHR拦截未启用或无自定义数据');
    }
  }

  return originalXHRSend.call(this, data);
};

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  debugLog('收到消息:', request);

  if (request.action === 'updateInterceptStatus') {
    interceptEnabled = request.enabled;
    debugLog('✅ 拦截状态已更新:', interceptEnabled);
    sendResponse({ success: true });
  }

  if (request.action === 'updateCustomJson') {
    customJsonData = request.data;
    debugLog('✅ 自定义JSON数据已更新:', customJsonData);
    sendResponse({ success: true });
  }

  if (request.action === 'getInterceptorStatus') {
    const status = {
      enabled: interceptEnabled,
      hasCustomData: !!customJsonData
    };
    debugLog('返回拦截器状态:', status);
    sendResponse(status);
  }

  return true;
});

// 立即初始化
debugLog('立即初始化拦截器...');
initializeInterceptor();

// 页面加载完成后再次初始化
if (document.readyState === 'loading') {
  debugLog('页面正在加载，等待DOMContentLoaded...');
  document.addEventListener('DOMContentLoaded', () => {
    debugLog('DOMContentLoaded触发，重新初始化...');
    initializeInterceptor();
  });
} else {
  debugLog('页面已加载完成，再次初始化...');
  setTimeout(initializeInterceptor, 100);
}

// 添加额外的初始化检查
setTimeout(() => {
  debugLog('延迟初始化检查...');
  if (!interceptEnabled && !customJsonData) {
    debugLog('重新尝试初始化...');
    initializeInterceptor();
  }
}, 1000);

// 监听页面可见性变化
document.addEventListener('visibilitychange', () => {
  if (!document.hidden) {
    debugLog('页面变为可见，检查拦截器状态...');
    initializeInterceptor();
  }
});

debugLog('拦截器脚本加载完成');

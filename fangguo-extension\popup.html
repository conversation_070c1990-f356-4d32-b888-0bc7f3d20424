<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Fangguo扩展</title>
  <style>
    body {
      width: 300px;
      padding: 16px;
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f8f9fa;
    }
    
    .header {
      text-align: center;
      margin-bottom: 16px;
    }
    
    .header h1 {
      margin: 0;
      font-size: 18px;
      color: #333;
    }
    
    .status {
      padding: 12px;
      border-radius: 6px;
      margin-bottom: 16px;
      text-align: center;
    }
    
    .status.active {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .status.inactive {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    .controls {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    
    button {
      padding: 10px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.3s ease;
    }
    
    .btn-primary {
      background: #409eff;
      color: white;
    }
    
    .btn-primary:hover {
      background: #66b1ff;
    }
    
    .btn-secondary {
      background: #6c757d;
      color: white;
    }
    
    .btn-secondary:hover {
      background: #5a6268;
    }
    
    .info {
      font-size: 12px;
      color: #666;
      text-align: center;
      margin-top: 16px;
      line-height: 1.4;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>Fangguo扩展</h1>
  </div>
  
  <div id="status" class="status inactive">
    <div id="status-text">未检测到目标页面</div>
  </div>
  
  <div id="intercept-status" class="status inactive">
    <div id="intercept-text">网络拦截已关闭</div>
  </div>

  <div class="controls">
    <button id="refresh-btn" class="btn-primary">刷新检测</button>
    <button id="toggle-btn" class="btn-secondary">切换功能</button>
    <button id="intercept-btn" class="btn-primary">开启拦截</button>
    <button id="edit-json-btn" class="btn-secondary">编辑JSON</button>
  </div>
  
  <div class="info">
    <p>此扩展会在 fangguo.com/business/order 页面自动添加自定义输入框。</p>
    <p>开启拦截后，会自动替换 check-exists API 的请求载荷。</p>
    <p>如果页面需要登录，请先登录后再访问目标页面。</p>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>

# Fangguo Order Page Enhancer

这是一个Chrome浏览器扩展，用于在fangguo.com的订单页面添加自定义输入框。

## 功能特性

- 自动检测 `https://fangguo.com/business/order` 页面
- 在指定的HTML元素位置添加自定义输入框
- 支持页面动态加载和URL变化检测
- 提供弹出窗口界面进行状态监控和控制

## 安装方法

1. 打开Chrome浏览器
2. 进入扩展管理页面 (`chrome://extensions/`)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择此项目文件夹

## 文件结构

```
fangguo-extension/
├── manifest.json       # 扩展配置文件
├── content.js          # 内容脚本，负责页面修改
├── styles.css          # 自定义样式
├── popup.html          # 弹出窗口界面
├── popup.js            # 弹出窗口逻辑
├── icons/              # 图标文件夹
└── README.md           # 说明文档
```

## 使用说明

1. 安装扩展后，访问 `https://fangguo.com/business/order` 页面
2. 如果页面需要登录，请先完成登录
3. 扩展会自动检测页面并在指定位置添加输入框
4. 点击浏览器工具栏中的扩展图标可以查看状态和进行控制

## 技术实现

- 使用Manifest V3规范
- 内容脚本监听URL变化和DOM变化
- MutationObserver确保动态内容加载完成后执行
- 支持SPA应用的路由变化检测

## 注意事项

- 扩展仅在fangguo.com域名下工作
- 需要页面完全加载后才能正确添加元素
- 如果页面结构发生变化，可能需要更新选择器

## 开发和调试

- 在Chrome开发者工具的Console中可以看到扩展的日志输出
- 修改代码后需要在扩展管理页面重新加载扩展
- 可以通过弹出窗口进行功能测试和状态检查

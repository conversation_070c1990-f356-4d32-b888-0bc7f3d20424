# Fangguo Order Page Enhancer

这是一个Chrome浏览器扩展，用于在fangguo.com的订单页面添加自定义输入框。

## 功能特性

- 自动检测 `https://fangguo.com/business/order` 页面
- 在指定的HTML元素位置添加自定义输入框
- **网络请求拦截**: 拦截 `check-exists` API 请求并替换载荷内容
- **自定义JSON数据**: 支持编辑和保存自定义的请求数据
- 支持页面动态加载和URL变化检测
- 提供弹出窗口界面进行状态监控和控制
- 专用JSON编辑器，支持格式化和验证

## 安装方法

1. 打开Chrome浏览器
2. 进入扩展管理页面 (`chrome://extensions/`)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择此项目文件夹

## 文件结构

```
fangguo-extension/
├── manifest.json           # 扩展配置文件
├── background.js           # 后台脚本，处理数据存储
├── network-interceptor.js  # 网络请求拦截器
├── content.js              # 内容脚本，负责页面修改
├── styles.css              # 自定义样式
├── popup.html              # 弹出窗口界面
├── popup.js                # 弹出窗口逻辑
├── json-editor.html        # JSON编辑器页面
├── json-editor.js          # JSON编辑器逻辑
├── icons/                  # 图标文件夹
└── README.md               # 说明文档
```

## 使用说明

### 基本功能
1. 安装扩展后，访问 `https://fangguo.com/business/order` 页面
2. 如果页面需要登录，请先完成登录
3. 扩展会自动检测页面并在指定位置添加输入框
4. 点击浏览器工具栏中的扩展图标可以查看状态和进行控制

### 网络拦截功能
1. 在弹出窗口中点击"开启拦截"按钮
2. 点击"编辑JSON"打开专用编辑器
3. 在编辑器中修改自定义的请求数据
4. 保存后，所有对 `check-exists` API 的请求都会使用自定义数据
5. 可以随时关闭拦截功能恢复正常请求

## 技术实现

- 使用Manifest V3规范
- 内容脚本监听URL变化和DOM变化
- MutationObserver确保动态内容加载完成后执行
- 支持SPA应用的路由变化检测

## 注意事项

- 扩展仅在fangguo.com域名下工作
- 需要页面完全加载后才能正确添加元素
- 如果页面结构发生变化，可能需要更新选择器

## 故障排除

如果遇到"Could not establish connection"错误：

1. **确保页面完全加载** - 等待页面完全加载后再使用扩展功能
2. **刷新页面** - 有时需要刷新页面让内容脚本重新加载
3. **重新加载扩展** - 在扩展管理页面点击刷新按钮
4. **检查控制台** - 打开开发者工具查看是否有错误信息
5. **手动注入** - 使用弹出窗口中的"注入脚本"按钮

## 开发和调试

- 在Chrome开发者工具的Console中可以看到扩展的日志输出
- 修改代码后需要在扩展管理页面重新加载扩展
- 可以通过弹出窗口进行功能测试和状态检查
- 扩展会自动检测内容脚本是否加载，如果没有会尝试动态注入

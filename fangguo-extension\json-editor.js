// JSON编辑器逻辑
let originalData = null;

// 显示状态消息
function showStatus(message, type = 'info') {
  const statusElement = document.getElementById('status');
  statusElement.textContent = message;
  statusElement.className = `status ${type}`;
  
  // 3秒后清除状态
  setTimeout(() => {
    statusElement.textContent = '';
    statusElement.className = 'status';
  }, 3000);
}

// 加载当前JSON数据
async function loadJsonData() {
  try {
    const response = await chrome.runtime.sendMessage({ action: 'getCustomJson' });
    originalData = response.data;
    
    const editor = document.getElementById('json-editor');
    editor.value = JSON.stringify(originalData, null, 2);
    
    showStatus('JSON数据已加载', 'success');
  } catch (error) {
    console.error('加载JSON数据失败:', error);
    showStatus('加载数据失败', 'error');
  }
}

// 格式化JSON
function formatJson() {
  const editor = document.getElementById('json-editor');
  const content = editor.value.trim();
  
  if (!content) {
    showStatus('请先输入JSON数据', 'error');
    return;
  }
  
  try {
    const parsed = JSON.parse(content);
    editor.value = JSON.stringify(parsed, null, 2);
    showStatus('JSON已格式化', 'success');
  } catch (error) {
    showStatus('JSON格式错误: ' + error.message, 'error');
  }
}

// 验证JSON
function validateJson() {
  const editor = document.getElementById('json-editor');
  const content = editor.value.trim();
  
  if (!content) {
    showStatus('请先输入JSON数据', 'error');
    return;
  }
  
  try {
    JSON.parse(content);
    showStatus('JSON格式正确', 'success');
  } catch (error) {
    showStatus('JSON格式错误: ' + error.message, 'error');
  }
}

// 保存JSON数据
async function saveJsonData() {
  const editor = document.getElementById('json-editor');
  const content = editor.value.trim();
  
  if (!content) {
    showStatus('请先输入JSON数据', 'error');
    return;
  }
  
  try {
    const parsedData = JSON.parse(content);
    
    await chrome.runtime.sendMessage({ 
      action: 'setCustomJson', 
      data: parsedData 
    });
    
    // 通知所有fangguo.com标签页更新数据
    const tabs = await chrome.tabs.query({ url: 'https://fangguo.com/*' });
    for (const tab of tabs) {
      try {
        await chrome.tabs.sendMessage(tab.id, { 
          action: 'updateCustomJson', 
          data: parsedData 
        });
      } catch (error) {
        console.log('无法通知标签页:', tab.id);
      }
    }
    
    originalData = parsedData;
    showStatus('JSON数据已保存并同步', 'success');
    
  } catch (error) {
    showStatus('保存失败: ' + error.message, 'error');
  }
}

// 重置到原始数据
function resetJsonData() {
  if (originalData) {
    const editor = document.getElementById('json-editor');
    editor.value = JSON.stringify(originalData, null, 2);
    showStatus('已重置到原始数据', 'success');
  } else {
    showStatus('没有原始数据可重置', 'error');
  }
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', async () => {
  await loadJsonData();
  
  // 绑定事件
  document.getElementById('format-btn').addEventListener('click', formatJson);
  document.getElementById('validate-btn').addEventListener('click', validateJson);
  document.getElementById('save-btn').addEventListener('click', saveJsonData);
  document.getElementById('reset-btn').addEventListener('click', resetJsonData);
  
  // 添加快捷键支持
  document.getElementById('json-editor').addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.key === 's') {
      e.preventDefault();
      saveJsonData();
    }
    
    if (e.ctrlKey && e.key === 'f') {
      e.preventDefault();
      formatJson();
    }
  });
});

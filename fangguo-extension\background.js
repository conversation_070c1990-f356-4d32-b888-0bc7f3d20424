console.log('Fangguo扩展后台脚本已加载');

// 存储自定义JSON数据的键
const CUSTOM_JSON_KEY = 'fangguo_custom_json';
const INTERCEPT_ENABLED_KEY = 'fangguo_intercept_enabled';

// 默认的自定义JSON数据
const DEFAULT_CUSTOM_JSON = {
  "pageNo": 1,
  "pageSize": 50,
  "queryStatus": 1,
  "shopId": "",
  "tidStrs": "自定义订单号1,自定义订单号2",
  "orderType": null,
  "remarkQuery": "",
  "timeTypeQuery": 0,
  "timeBegin": "2025-07-22 00:00:00",
  "timeEnd": "2025-07-28 23:59:59",
  "storeIdList": [],
  "cpCode": "",
  "outerOrderStatusList": [],
  "waybillType": "",
  "flagListQuery": [],
  "receiverProvinceCodeList": [],
  "receiverCity": "",
  "cpNumStrs": "自定义快递单号",
  "inquiryModeByTitle": 1,
  "title": "自定义商品标题",
  "inquiryModeBySkuCode": 1,
  "shopMappingSkuRange": 1,
  "shopMappingSku": "自定义商家编码",
  "inquiryModeByOuterIid": 1,
  "outerIid": "自定义原商家编码",
  "receiverName": "自定义收货姓名",
  "receiverMobileStrs": "自定义收货手机号",
  "buyerNickStrs": "自定义买家昵称",
  "receiverAddressStrs": "",
  "tradeIdStr": "",
  "barcode": "自定义拿货条码",
  "shopRemark": "自定义卖家备注",
  "buyerRemark": "自定义买家留言",
  "factoryRemark": "自定义厂家备注",
  "numEqualType": null,
  "num": 1,
  "shipStatusOrder": "",
  "lockStatusOrder": "",
  "discardStatusOrder": "",
  "bigPackageLPNumberStrs": "",
  "milePackageStatus": "",
  "existCpNum": "",
  "waybillPrintStatus": "",
  "urgencyType": "",
  "isPicNum": "",
  "inquiryModeByProperties": 1,
  "skuPropertiesRange": 1,
  "skuProperties": "自定义商品规格",
  "factoryId": 3002828,
  "custodyType": null,
  "hourType": 0,
  "logisticsStatus": "",
  "isCustomGoods": "",
  "decodeStatus": "",
  "tagListQuery": [],
  "numIids": "",
  "payment": 0,
  "paymentEqualType": null,
  "factoryLackPicStatus": "",
  "decodeErrorTypeList": [],
  "autoPush": "",
  "inventoryStatus": "",
  "sort": 1,
  "countTimeType": "",
  "countTimeStart": "",
  "countTimeEnd": "",
  "jitSendStatus": "",
  "inquiryModeByProductSn": 1,
  "productSn": "",
  "categoryIdList": [],
  "customIdStr": "自定义定制ID1,自定义定制ID2",
  "inquiryModeByGiftCode": 0,
  "giftCode": "自定义赠品编码",
  "combinationGoodsType": null,
  "deductWarehouseName": "",
  "exportSkuImg": false,
  "customOrderRemark": ""
};

// 初始化存储
chrome.runtime.onInstalled.addListener(async () => {
  console.log('扩展已安装，初始化存储');
  
  // 检查是否已有自定义JSON数据
  const result = await chrome.storage.local.get([CUSTOM_JSON_KEY, INTERCEPT_ENABLED_KEY]);
  
  if (!result[CUSTOM_JSON_KEY]) {
    await chrome.storage.local.set({
      [CUSTOM_JSON_KEY]: DEFAULT_CUSTOM_JSON
    });
    console.log('已设置默认自定义JSON数据');
  }
  
  if (result[INTERCEPT_ENABLED_KEY] === undefined) {
    await chrome.storage.local.set({
      [INTERCEPT_ENABLED_KEY]: false
    });
    console.log('已设置默认拦截状态为关闭');
  }
});

// 监听来自popup和content script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'getCustomJson') {
    chrome.storage.local.get([CUSTOM_JSON_KEY]).then(result => {
      sendResponse({ data: result[CUSTOM_JSON_KEY] || DEFAULT_CUSTOM_JSON });
    });
    return true;
  }
  
  if (request.action === 'setCustomJson') {
    chrome.storage.local.set({
      [CUSTOM_JSON_KEY]: request.data
    }).then(() => {
      console.log('自定义JSON数据已更新');
      sendResponse({ success: true });
    });
    return true;
  }
  
  if (request.action === 'getInterceptStatus') {
    chrome.storage.local.get([INTERCEPT_ENABLED_KEY]).then(result => {
      sendResponse({ enabled: result[INTERCEPT_ENABLED_KEY] || false });
    });
    return true;
  }
  
  if (request.action === 'setInterceptStatus') {
    chrome.storage.local.set({
      [INTERCEPT_ENABLED_KEY]: request.enabled
    }).then(() => {
      console.log('拦截状态已更新:', request.enabled);
      sendResponse({ success: true });
    });
    return true;
  }
});

// 导出给其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    CUSTOM_JSON_KEY,
    INTERCEPT_ENABLED_KEY,
    DEFAULT_CUSTOM_JSON
  };
}

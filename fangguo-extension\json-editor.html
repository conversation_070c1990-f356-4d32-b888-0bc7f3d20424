<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>JSON数据编辑器</title>
  <style>
    body {
      width: 600px;
      height: 500px;
      padding: 16px;
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f8f9fa;
    }
    
    .header {
      text-align: center;
      margin-bottom: 16px;
    }
    
    .header h1 {
      margin: 0;
      font-size: 18px;
      color: #333;
    }
    
    .editor-container {
      display: flex;
      flex-direction: column;
      height: calc(100% - 100px);
    }
    
    textarea {
      flex: 1;
      padding: 12px;
      border: 1px solid #dcdfe6;
      border-radius: 6px;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      resize: none;
      outline: none;
    }
    
    textarea:focus {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
    
    .controls {
      display: flex;
      gap: 8px;
      margin-top: 16px;
      justify-content: center;
    }
    
    button {
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.3s ease;
    }
    
    .btn-primary {
      background: #409eff;
      color: white;
    }
    
    .btn-primary:hover {
      background: #66b1ff;
    }
    
    .btn-secondary {
      background: #6c757d;
      color: white;
    }
    
    .btn-secondary:hover {
      background: #5a6268;
    }
    
    .btn-success {
      background: #67c23a;
      color: white;
    }
    
    .btn-success:hover {
      background: #85ce61;
    }
    
    .status {
      text-align: center;
      margin-top: 8px;
      font-size: 12px;
      color: #666;
    }
    
    .error {
      color: #f56c6c;
    }
    
    .success {
      color: #67c23a;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>自定义JSON数据编辑器</h1>
  </div>
  
  <div class="editor-container">
    <textarea id="json-editor" placeholder="在此编辑JSON数据..."></textarea>
  </div>
  
  <div class="controls">
    <button id="format-btn" class="btn-secondary">格式化</button>
    <button id="validate-btn" class="btn-primary">验证</button>
    <button id="save-btn" class="btn-success">保存</button>
    <button id="reset-btn" class="btn-secondary">重置</button>
  </div>
  
  <div id="status" class="status"></div>
  
  <script src="json-editor.js"></script>
</body>
</html>
